{% extends "base.html" %}
{% load i18n %}

{% block content %}
<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-cogs me-2"></i>
                        {% trans "System Settings" %}
                    </h5>
                </div>
                <div class="card-body">
                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        {% endfor %}
                    {% endif %}

                    <form method="post" class="needs-validation" novalidate>
                        {% csrf_token %}
                        
                        <div class="mb-4">
                            <h6 class="text-muted mb-3">{% trans "General Settings" %}</h6>
                            
                            <div class="mb-3">
                                <label for="{{ form.site_name.id_for_label }}" class="form-label">
                                    {{ form.site_name.label }}
                                </label>
                                {{ form.site_name }}
                                {% if form.site_name.help_text %}
                                    <div class="form-text">{{ form.site_name.help_text }}</div>
                                {% endif %}
                                {% if form.site_name.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.site_name.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>

                            <div class="mb-3">
                                <label for="{{ form.maintenance_mode.id_for_label }}" class="form-label">
                                    {{ form.maintenance_mode.label }}
                                </label>
                                {{ form.maintenance_mode }}
                                {% if form.maintenance_mode.help_text %}
                                    <div class="form-text">{{ form.maintenance_mode.help_text }}</div>
                                {% endif %}
                                {% if form.maintenance_mode.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.maintenance_mode.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="{% url 'accounts:home' %}" class="btn btn-secondary me-md-2">
                                {% trans "Cancel" %}
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                {% trans "Save Settings" %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
