from typing import Any, Dict
import logging
from django.contrib.auth import login, authenticate, logout, update_session_auth_hash
from django.contrib.auth.tokens import default_token_generator
from django.utils.html import strip_tags
from django.utils import timezone
from django.contrib.auth.decorators import login_required, user_passes_test
from django.contrib.auth.mixins import LoginRequiredMixin, UserPassesTestMixin
from django.contrib import messages
from django.core.exceptions import PermissionDenied
from django.shortcuts import render, redirect, get_object_or_404
from django.urls import reverse_lazy, reverse
from django.utils.decorators import method_decorator
from django.utils.http import urlsafe_base64_encode, urlsafe_base64_decode
from django.utils.encoding import force_bytes, force_str
from django.utils.translation import gettext_lazy as _
from django.views.generic import (
    View, UpdateView, ListView, DetailView,
    <PERSON><PERSON><PERSON><PERSON><PERSON>, DeleteView, TemplateView
)
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_protect
from django.core.mail import send_mail
from django.template.loader import render_to_string
from django.conf import settings
from django.http import JsonResponse, HttpResponseRedirect, HttpResponse
from django.db.models import Q
from django.core.paginator import Paginator
from django.core.exceptions import ValidationError

from .forms import (
    CustomUserCreationForm, CustomLoginForm, ResendVerificationForm,
    UserProfileUpdateForm, ChangePasswordForm, ResetPasswordForm,
    EmailVerificationForm
)
from .models import CustomUser, UserActivityLog, UserSettings, UserQualification
from .tokens import account_activation_token
from .decorators import check_recaptcha
from .mixins import SuperUserRequiredMixin

class BaseContextMixin:
    """Base mixin to add common context data to views"""
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['site_name'] = settings.SITE_NAME
        return context

class AuthenticationViewMixin:
    """Mixin for authentication-related views"""
    def dispatch(self, request, *args, **kwargs):
        if request.user.is_authenticated:
            messages.info(request, _('You are already logged in.'))
            return redirect('home')
        return super().dispatch(request, *args, **kwargs)

class RegisterView(AuthenticationViewMixin, BaseContextMixin, CreateView):
    model = CustomUser
    form_class = CustomUserCreationForm
    template_name = 'accounts/auth/register.html'
    success_url = reverse_lazy('accounts:login')

    # @method_decorator(check_recaptcha)
    def form_valid(self, form):
        if not self.request.recaptcha_is_valid:
            messages.error(self.request, _('Invalid reCAPTCHA. Please try again.'))
            return self.form_invalid(form)

        user = form.save(commit=False)
        user.is_active = False
        user.save()

        self.send_verification_email(user)
        messages.success(
            self.request,
            _('Registration successful! Please check your email to verify your account.')
        )
        return super().form_valid(form)

    def send_verification_email(self, user):
        token = account_activation_token.make_token(user)
        uid = urlsafe_base64_encode(force_bytes(user.pk))
        verification_url = reverse('accounts:verify_email',
                                 kwargs={'uidb64': uid, 'token': token})

        context = {
            'user': user,
            'verification_url': f"{settings.DOMAIN}{verification_url}",
            'site_name': settings.SITE_NAME,
            'expiry_hours': 48,  # Token expiry time
        }

        subject = f"{settings.SITE_NAME} - Verify your email address"
        html_message = render_to_string(
            'accounts/email/verification_email.html',
            context
        )
        plain_message = render_to_string(
            'accounts/email/verification_email.txt',
            context
        )

        try:
            send_mail(
                subject=subject,
                message=plain_message,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[user.email],
                html_message=html_message,
                fail_silently=False,
            )
        except Exception as e:
            user.delete()  # Rollback user creation if email fails
            messages.error(
                self.request,
                _('Registration failed. Please try again later.')
            )
            return redirect('accounts:register')

class LoginView(AuthenticationViewMixin, BaseContextMixin, View):
    template_name = 'accounts/auth/login.html'
    form_class = CustomLoginForm

    def get(self, request):
        return render(request, self.template_name, {'form': self.form_class()})


    def get_success_url(self, user):
        if user.is_superuser:
            return redirect('admin:index')
        elif user.user_type == 2:  # Dentist
            return redirect('Dentists:dashboard')
        else:
            return redirect('home')



    # @method_decorator(check_recaptcha)
    def post(self, request):
        form = self.form_class(data=request.POST)
        if form.is_valid(): # and request.recaptcha_is_valid:
            user = form.get_user()
            login(request, user)

            # Log user activity
            UserActivityLog.objects.create(
                user=user,
                action='login',
                ip_address=self.get_client_ip(request),
                user_agent=request.META.get('HTTP_USER_AGENT'),
                details={'login_type': 'regular'}
            )

            # Handle remember me
            if not form.cleaned_data.get('remember_me'):
                request.session.set_expiry(0)

            # Get the 'next' parameter if it exists
            next_url = request.GET.get('next')
            if next_url and self.is_safe_url(next_url):
                return redirect(next_url)

            # Redirect based on user type
            if user.is_superuser:
                return redirect('admin:index')
            elif user.user_type == 2:  # Dentist
                return redirect('Dentists:dashboard')
            else:
                return redirect('home')

        return render(request, self.template_name, {'form': form})

    def get_client_ip(self, request):
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        return (x_forwarded_for.split(',')[0] if x_forwarded_for
                else request.META.get('REMOTE_ADDR'))

    def is_safe_url(self, url):
        from django.utils.http import url_has_allowed_host_and_scheme
        return url_has_allowed_host_and_scheme(
            url=url,
            allowed_hosts={self.request.get_host()},
            require_https=self.request.is_secure(),
        )

class ProfileView(LoginRequiredMixin, BaseContextMixin, UpdateView):
    model = CustomUser
    form_class = UserProfileUpdateForm
    template_name = 'accounts/profile/profile.html'
    success_url = reverse_lazy('accounts:profile')

    def get_object(self, queryset=None):
        return self.request.user

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context.update({
            'user_settings': self.request.user.settings,
            'qualifications': self.request.user.qualifications.all(),
            'recent_activity': self.request.user.activity_logs.order_by(
                '-timestamp'
            )[:5],
        })
        return context

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, _('Profile updated successfully.'))

        # Log the profile update
        UserActivityLog.objects.create(
            user=self.request.user,
            action='profile_update',
            ip_address=self.request.META.get('REMOTE_ADDR'),
            details={'updated_fields': list(form.changed_data)}
        )

        return response

# @method_decorator(login_required, name='dispatch')
class UserSettingsView(LoginRequiredMixin, BaseContextMixin, UpdateView):
    model = UserSettings
    fields = [
        'email_notifications',
        'sms_notifications',
        'theme',
        'preferred_language',
        'items_per_page'
    ]
    template_name = 'accounts/profile/settings.html'
    success_url = reverse_lazy('accounts:profile')

    def get_object(self, queryset=None):
        settings, created = UserSettings.objects.get_or_create(user=self.request.user)
        return settings

    def form_valid(self, form):
        messages.success(self.request, _('Settings updated successfully.'))
        return super().form_valid(form)


from django.views.generic import UpdateView
from django.contrib import messages
from django.urls import reverse_lazy
from .models import UserSettings

class UserSettingsUpdateView(LoginRequiredMixin, UpdateView):
    model = UserSettings
    fields = [
        'email_notifications',
        'sms_notifications',
        'theme',
        'preferred_language',
        'items_per_page'
    ]
    template_name = 'accounts/profile/settings_update.html'
    success_url = reverse_lazy('accounts:profile')

    def get_object(self, queryset=None):
        # Get or create settings for the current user
        settings, created = UserSettings.objects.get_or_create(user=self.request.user)
        return settings

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, _('Settings updated successfully.'))
        return response

from django.views.generic import View
from django.shortcuts import render, redirect
from django.contrib import messages
from django.utils.translation import gettext_lazy as _

class TwoFactorSetupView(LoginRequiredMixin, View):
    template_name = 'accounts/auth/setup_2fa.html'

    def get(self, request):
        context = {
            'user': request.user,
            # Add any additional context you need
        }
        return render(request, self.template_name, context)

    def post(self, request):
        # Handle 2FA setup logic here
        try:
            # Your 2FA setup implementation
            messages.success(request, _('Two-factor authentication has been set up successfully.'))
            return redirect('accounts:profile')
        except Exception as e:
            messages.error(request, str(e))
            return render(request, self.template_name)

class QualificationManagementMixin:
    """Mixin for qualification-related views"""
    def get_queryset(self):
        return UserQualification.objects.filter(user=self.request.user)

class QualificationListView(LoginRequiredMixin, QualificationManagementMixin,
                          BaseContextMixin, ListView):
    model = UserQualification
    template_name = 'accounts/profile/qualifications.html'
    context_object_name = 'qualifications'
    paginate_by = 10

class QualificationCreateView(LoginRequiredMixin, QualificationManagementMixin,
                            BaseContextMixin, CreateView):
    model = UserQualification
    template_name = 'accounts/profile/qualification_form.html'
    fields = ['title', 'institution', 'date_obtained', 'document']
    success_url = reverse_lazy('accounts:qualifications')

    def form_valid(self, form):
        form.instance.user = self.request.user
        response = super().form_valid(form)
        messages.success(self.request, _('Qualification added successfully.'))
        return response

class QualificationDeleteView(LoginRequiredMixin, QualificationManagementMixin,
                            BaseContextMixin, DeleteView):
    model = UserQualification
    template_name = 'accounts/profile/qualification_confirm_delete.html'
    success_url = reverse_lazy('accounts:qualifications')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(request, _('Qualification deleted successfully.'))
        return response

from django.views.generic import ListView
from django.utils.translation import gettext_lazy as _
from django.contrib.auth.mixins import LoginRequiredMixin
from django.db.models import Q
from .models import UserActivityLog

class ActivityLogView(LoginRequiredMixin, ListView):
    model = UserActivityLog
    template_name = 'accounts/profile/activity_log.html'
    context_object_name = 'logs'
    paginate_by = 20
    ordering = ['-timestamp']

    def get_queryset(self):
        queryset = UserActivityLog.objects.filter(user=self.request.user)

        # Search
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(action__icontains=search) |
                Q(ip_address__icontains=search) |
                Q(user_agent__icontains=search)
            )

        # Date range
        start_date = self.request.GET.get('start_date')
        if start_date:
            queryset = queryset.filter(timestamp__date__gte=start_date)

        end_date = self.request.GET.get('end_date')
        if end_date:
            queryset = queryset.filter(timestamp__date__lte=end_date)

        # Action type filter
        actions = self.request.GET.getlist('action')
        if actions:
            queryset = queryset.filter(action__in=actions)

        # Status filter
        success = self.request.GET.getlist('success')
        if success:
            queryset = queryset.filter(success__in=[s == 'true' for s in success])

        # Ordering
        order_by = self.request.GET.get('order_by', '-timestamp')
        return queryset.order_by(order_by)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        queryset = self.get_queryset()

        context.update({
            'login_count': queryset.filter(action='login').count(),
            'security_count': queryset.filter(action='security').count(),
            'failed_count': queryset.filter(success=False).count(),
            'action_types': UserActivityLog.ACTION_TYPES,
            'selected_actions': self.request.GET.getlist('action'),
            'selected_status': self.request.GET.getlist('success'),
            'order_by': self.request.GET.get('order_by', '-timestamp'),
        })

        return context


import csv
from django.http import HttpResponse
from django.views import View
from reportlab.lib import colors
from reportlab.lib.pagesizes import letter
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle
from io import BytesIO

class ExportActivityLogView(LoginRequiredMixin, View):
    def get(self, request, *args, **kwargs):
        # Get the format from query parameters (default to CSV)
        export_format = request.GET.get('format', 'csv')

        # Get activity logs
        logs = UserActivityLog.objects.filter(user=request.user).order_by('-timestamp')

        # Apply filters if any
        if request.GET.get('start_date'):
            logs = logs.filter(timestamp__date__gte=request.GET.get('start_date'))
        if request.GET.get('end_date'):
            logs = logs.filter(timestamp__date__lte=request.GET.get('end_date'))
        if request.GET.getlist('action'):
            logs = logs.filter(action__in=request.GET.getlist('action'))
        if request.GET.getlist('success'):
            logs = logs.filter(success__in=[s == 'true' for s in request.GET.getlist('success')])

        if export_format == 'csv':
            return self._export_csv(logs)
        elif export_format == 'pdf':
            return self._export_pdf(logs)
        else:
            return HttpResponse('Unsupported format', status=400)

    def _export_csv(self, logs):
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename="activity_log.csv"'

        writer = csv.writer(response)
        # Write header
        writer.writerow([
            'Timestamp',
            'Action',
            'IP Address',
            'Browser',
            'Status',
            'Details'
        ])

        # Write data
        for log in logs:
            writer.writerow([
                log.timestamp.strftime('%Y-%m-%d %H:%M:%S'),
                log.get_action_display(),
                log.ip_address or 'N/A',
                log.user_agent or 'N/A',
                'Success' if log.success else 'Failed',
                str(log.details) if log.details else 'N/A'
            ])

        return response

    def _export_pdf(self, logs):
        response = HttpResponse(content_type='application/pdf')
        response['Content-Disposition'] = 'attachment; filename="activity_log.pdf"'

        buffer = BytesIO()
        doc = SimpleDocTemplate(buffer, pagesize=letter)
        elements = []

        # Define data for table
        data = [['Timestamp', 'Action', 'IP Address', 'Status']]  # Header row
        for log in logs:
            data.append([
                log.timestamp.strftime('%Y-%m-%d %H:%M:%S'),
                log.get_action_display(),
                log.ip_address or 'N/A',
                'Success' if log.success else 'Failed'
            ])

        # Create table
        table = Table(data)
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 14),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('TEXTCOLOR', (0, 1), (-1, -1), colors.black),
            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 1), (-1, -1), 12),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ]))

        elements.append(table)
        doc.build(elements)

        # Get PDF from buffer
        pdf = buffer.getvalue()
        buffer.close()
        response.write(pdf)

        return response




class PasswordManagementView(LoginRequiredMixin, BaseContextMixin, View):
    template_name = 'accounts/profile/change_password.html'
    form_class = ChangePasswordForm

    def get(self, request):
        form = self.form_class(request.user)
        return render(request, self.template_name, {'form': form})

    def post(self, request):
        form = self.form_class(request.user, request.POST)
        if form.is_valid():
            user = form.save()

            # Log password change
            UserActivityLog.objects.create(
                user=user,
                action='password_change',
                ip_address=request.META.get('REMOTE_ADDR'),
                details={'method': 'manual_change'}
            )

            # Update session to prevent session fixation
            update_session_auth_hash(request, user)

            messages.success(request, _('Your password was successfully updated.'))
            return redirect('accounts:profile')

        return render(request, self.template_name, {'form': form})

class PasswordResetView(AuthenticationViewMixin, BaseContextMixin, View):
    template_name = 'accounts/auth/password_reset.html'
    form_class = ResetPasswordForm
    email_template_name = 'accounts/email/password_reset_email.html'
    email_subject_template_name = 'accounts/email/password_reset_subject.txt'

    def get(self, request):
        return render(request, self.template_name, {'form': self.form_class()})

    def post(self, request):
        form = self.form_class(request.POST)
        if form.is_valid():
            email = form.cleaned_data['email']
            try:
                user = CustomUser.objects.get(email=email)
                self.send_password_reset_email(user)
                messages.success(
                    request,
                    _('Password reset instructions have been sent to your email.')
                )
                return redirect('accounts:login')
            except CustomUser.DoesNotExist:
                # Still show success message to prevent email enumeration
                messages.success(
                    request,
                    _('If an account exists with this email, '
                      'you will receive password reset instructions.')
                )
                return redirect('accounts:login')

        return render(request, self.template_name, {'form': form})

    def send_password_reset_email(self, user):
        token = default_token_generator.make_token(user)
        uid = urlsafe_base64_encode(force_bytes(user.pk))
        reset_url = reverse('accounts:password_reset_confirm',
                          kwargs={'uidb64': uid, 'token': token})

        context = {
            'user': user,
            'reset_url': f"{settings.DOMAIN}{reset_url}",
            'site_name': settings.SITE_NAME,
            'expiry_hours': 24,
        }

        subject = render_to_string(self.email_subject_template_name, context)
        subject = ''.join(subject.splitlines())  # Remove newlines
        html_message = render_to_string(self.email_template_name, context)
        plain_message = strip_tags(html_message)

        send_mail(
            subject=subject,
            message=plain_message,
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[user.email],
            html_message=html_message,
            fail_silently=False,
        )

class EmailVerificationView(BaseContextMixin, View):
    """Handle email verification links"""

    def get(self, request, uidb64, token):
        try:
            uid = force_str(urlsafe_base64_decode(uidb64))
            user = CustomUser.objects.get(pk=uid)

            if not account_activation_token.check_token(user, token):
                raise ValidationError(_('Verification link is invalid or expired'))

            if user.is_active:
                messages.info(request, _('Your email is already verified.'))
                return redirect('accounts:login')

            user.is_active = True
            user.email_verified = True
            user.email_verified_at = timezone.now()
            user.save(update_fields=['is_active', 'email_verified',
                                   'email_verified_at'])

            UserActivityLog.objects.create(
                user=user,
                action='email_verification',
                ip_address=request.META.get('REMOTE_ADDR'),
                details={'method': 'email_link'}
            )

            messages.success(
                request,
                _('Email verified successfully. You can now log in.')
            )
            return redirect('accounts:login')

        except (TypeError, ValueError, OverflowError, CustomUser.DoesNotExist,
                ValidationError) as e:
            messages.error(
                request,
                _('Verification link is invalid or expired. '
                  'Please request a new one.')
            )
            return redirect('accounts:resend_verification')

class ResendVerificationView(AuthenticationViewMixin, BaseContextMixin, View):
    template_name = 'accounts/auth/resend_verification.html'
    form_class = ResendVerificationForm

    def get(self, request):
        return render(request, self.template_name, {'form': self.form_class()})

    def post(self, request):
        form = self.form_class(request.POST)
        if form.is_valid():
            email = form.cleaned_data['email']
            try:
                user = CustomUser.objects.get(email=email, is_active=False)
                self.send_verification_email(user)
                messages.success(
                    request,
                    _('Verification email has been resent. '
                      'Please check your inbox.')
                )
                return redirect('accounts:login')
            except CustomUser.DoesNotExist:
                messages.error(
                    request,
                    _('No unverified account found with this email address.')
                )
        return render(request, self.template_name, {'form': form})

# @method_decorator(login_required, name='dispatch')
class ProfileImageUpdateView(View):
    def post(self, request):
        try:
            if 'profile_image' not in request.FILES:
                raise ValidationError(_('No image provided'))

            image = request.FILES['profile_image']

            # Validate image
            validate_image_file(image)

            # Process image
            processed_image = process_profile_image(image)

            # Save image
            request.user.profile_image = processed_image
            request.user.save(update_fields=['profile_image'])

            # Log activity
            UserActivityLog.objects.create(
                user=request.user,
                action='profile_image_update',
                ip_address=request.META.get('REMOTE_ADDR')
            )

            return JsonResponse({
                'success': True,
                'message': _('Profile image updated successfully.'),
                'image_url': request.user.profile_image.url
            })

        except ValidationError as e:
            return JsonResponse({
                'success': False,
                'message': str(e)
            }, status=400)
        except Exception as e:
            logger.error(f"Profile image update error: {str(e)}")
            return JsonResponse({
                'success': False,
                'message': _('An error occurred while updating profile image.')
            }, status=500)

@login_required
def logout_view(request):
    """Handle user logout with activity logging"""
    if request.user.is_authenticated:
        UserActivityLog.objects.create(
            user=request.user,
            action='logout',
            ip_address=request.META.get('REMOTE_ADDR'),
            details={'logout_type': 'manual'}
        )
        logout(request)
        messages.success(request, _('You have been successfully logged out.'))
    return redirect('accounts:login')

# Admin views
class UserManagementView(SuperUserRequiredMixin, BaseContextMixin, ListView):
    model = CustomUser
    template_name = 'accounts/admin/user_management.html'
    context_object_name = 'users'
    paginate_by = 25
    ordering = ['-date_joined']

    def get_queryset(self):
        queryset = super().get_queryset()
        search_query = self.request.GET.get('q')
        if search_query:
            queryset = queryset.filter(
                Q(email__icontains=search_query) |
                Q(first_name__icontains=search_query) |
                Q(last_name__icontains=search_query)
            )
        return queryset.select_related('settings')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context.update({
            'total_users': CustomUser.objects.count(),
            'active_users': CustomUser.objects.filter(is_active=True).count(),
            'staff_users': CustomUser.objects.filter(is_staff=True).count(),
            'search_query': self.request.GET.get('q', ''),
        })
        return context

# Error handling views
def custom_404(request, exception):
    return render(request, 'accounts/errors/404.html', status=404)

def custom_500(request):
    return render(request, 'accounts/errors/500.html', status=500)

def custom_403(request, exception):
    return render(request, 'accounts/errors/403.html', status=403)



# accounts/views.py

class UserDetailView(SuperUserRequiredMixin, BaseContextMixin, DetailView):
    model = CustomUser
    template_name = 'accounts/admin/user_detail.html'
    context_object_name = 'user_detail'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        user = self.get_object()
        context.update({
            'activity_logs': user.activity_logs.order_by('-timestamp')[:10],
            'qualifications': user.qualifications.all(),
            'total_logins': user.activity_logs.filter(action='login').count(),
            'last_login': user.activity_logs.filter(
                action='login'
            ).order_by('-timestamp').first(),
            'account_age': (timezone.now() - user.date_joined).days,
            'departments': user.departments.all(),
            'is_active': user.is_active,
            'is_staff': user.is_staff,
            'is_superuser': user.is_superuser,
            'date_joined': user.date_joined,
            # 'email_verified': user.email_verified,
        })
        return context

# accounts/views.py

from .forms import UserAdminForm  # Add this import at the top

class UserUpdateView(SuperUserRequiredMixin, BaseContextMixin, UpdateView):
    model = CustomUser
    form_class = UserAdminForm
    template_name = 'accounts/admin/user_edit.html'
    success_url = reverse_lazy('accounts:user_management')

    def form_valid(self, form):
        response = super().form_valid(form)
        user = self.object

        # Log the changes
        changed_fields = form.changed_data
        UserActivityLog.objects.create(
            user=self.request.user,  # Admin who made the changes
            action='user_update',
            ip_address=self.request.META.get('REMOTE_ADDR'),
            details={
                'modified_user_id': user.id,
                'modified_fields': changed_fields
            }
        )

        if 'password_change' in changed_fields:
            # Log password change separately
            UserActivityLog.objects.create(
                user=user,
                action='password_change',
                ip_address=self.request.META.get('REMOTE_ADDR'),
                details={'changed_by_admin': self.request.user.id}
            )

        messages.success(
            self.request,
            _('User "{}" has been updated successfully.').format(user.get_full_name())
        )
        return response

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['user_full_name'] = self.object.get_full_name()
        return context




class UserDeleteView(SuperUserRequiredMixin, BaseContextMixin, DeleteView):
    model = CustomUser
    template_name = 'accounts/admin/user_delete.html'
    success_url = reverse_lazy('accounts:user_management')
    context_object_name = 'user_to_delete'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        user = self.get_object()
        context.update({
            'user_full_name': user.get_full_name(),
            'user_email': user.email,
            'account_age': (timezone.now() - user.date_joined).days,
            'is_staff': user.is_staff,
            'is_superuser': user.is_superuser,
            # Add any related objects that will be affected
            'related_cases': getattr(user, 'cases', []).count(),
            'related_qualifications': user.qualifications.count(),
            'activity_logs': user.activity_logs.count(),
        })
        return context

    def send_verification_email(self, user):
        token = account_activation_token.make_token(user)
        uid = urlsafe_base64_encode(force_bytes(user.pk))
        verification_url = reverse('accounts:verify_email',
                                 kwargs={'uidb64': uid, 'token': token})

        context = {
            'user': user,
            'verification_url': f"{settings.DOMAIN}{verification_url}",
            'site_name': settings.SITE_NAME,
            'expiry_hours': 48,  # Token expiry time
        }

        subject = f"{settings.SITE_NAME} - Verify your email address"
        html_message = render_to_string(
            'accounts/email/verification_email.html',
            context
        )
        plain_message = render_to_string(
            'accounts/email/verification_email.txt',
            context
        )

        try:
            send_mail(
                subject=subject,
                message=plain_message,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[user.email],
                html_message=html_message,
                fail_silently=False,
            )
        except Exception as e:
            user.delete()  # Rollback user creation if email fails
            messages.error(
                self.request,
                _('Registration failed. Please try again later.')
            )
            return redirect('accounts:register')
from django.contrib.auth import login, authenticate, logout
from django.contrib.auth.decorators import login_required, user_passes_test
from django.contrib.auth.mixins import LoginRequiredMixin, UserPassesTestMixin
from django.contrib import messages
from django.core.exceptions import PermissionDenied
from django.shortcuts import render, redirect, get_object_or_404
from django.urls import reverse_lazy, reverse
from django.utils.decorators import method_decorator
from django.utils.http import urlsafe_base64_encode, urlsafe_base64_decode
from django.utils.encoding import force_bytes, force_str
from django.utils.translation import gettext_lazy as _
from django.views.generic import (
    View, UpdateView, ListView, DetailView,
    CreateView, DeleteView, TemplateView
)
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_protect
from django.core.mail import send_mail
from django.template.loader import render_to_string
from django.conf import settings
from django.http import JsonResponse, HttpResponseRedirect, HttpResponse
from django.db.models import Q
from django.core.paginator import Paginator
from django.core.exceptions import ValidationError

from .forms import (
    CustomUserCreationForm, CustomLoginForm, ResendVerificationForm,
    UserProfileUpdateForm, ChangePasswordForm, ResetPasswordForm,
    EmailVerificationForm
)
from .models import CustomUser, UserActivityLog, UserSettings, UserQualification
from .tokens import account_activation_token
from .decorators import check_recaptcha
from .mixins import SuperUserRequiredMixin

class BaseContextMixin:
    """Base mixin to add common context data to views"""
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['site_name'] = settings.SITE_NAME
        return context

class AuthenticationViewMixin:
    """Mixin for authentication-related views"""
    def dispatch(self, request, *args, **kwargs):
        if request.user.is_authenticated:
            messages.info(request, _('You are already logged in.'))
            return redirect('home')
        return super().dispatch(request, *args, **kwargs)

class RegisterView(AuthenticationViewMixin, BaseContextMixin, CreateView):
    model = CustomUser
    form_class = CustomUserCreationForm
    template_name = 'accounts/auth/register.html'
    success_url = reverse_lazy('accounts:login')

    # @method_decorator(check_recaptcha)
    def form_valid(self, form):
        if not self.request.recaptcha_is_valid:
            messages.error(self.request, _('Invalid reCAPTCHA. Please try again.'))
            return self.form_invalid(form)

        user = form.save(commit=False)
        user.is_active = False
        user.save()

        self.send_verification_email(user)
        messages.success(
            self.request,
            _('Registration successful! Please check your email to verify your account.')
        )
        return super().form_valid(form)

    def send_verification_email(self, user):
        token = account_activation_token.make_token(user)
        uid = urlsafe_base64_encode(force_bytes(user.pk))
        verification_url = reverse('accounts:verify_email',
                                 kwargs={'uidb64': uid, 'token': token})

        context = {
            'user': user,
            'verification_url': f"{settings.DOMAIN}{verification_url}",
            'site_name': settings.SITE_NAME,
            'expiry_hours': 48,  # Token expiry time
        }

        subject = f"{settings.SITE_NAME} - Verify your email address"
        html_message = render_to_string(
            'accounts/email/verification_email.html',
            context
        )
        plain_message = render_to_string(
            'accounts/email/verification_email.txt',
            context
        )

        try:
            send_mail(
                subject=subject,
                message=plain_message,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[user.email],
                html_message=html_message,
                fail_silently=False,
            )
        except Exception as e:
            user.delete()  # Rollback user creation if email fails
            messages.error(
                self.request,
                _('Registration failed. Please try again later.')
            )
            return redirect('accounts:register')

class LoginView(AuthenticationViewMixin, BaseContextMixin, View):
    template_name = 'accounts/auth/login.html'
    form_class = CustomLoginForm

    def get(self, request):
        return render(request, self.template_name, {'form': self.form_class()})


    def get_success_url(self, user):
        if user.is_superuser:
            return redirect('admin:index')
        elif user.user_type == 2:  # Dentist
            return redirect('Dentists:dashboard')
        else:
            return redirect('home')



    # @method_decorator(check_recaptcha)
    def post(self, request):
        form = self.form_class(data=request.POST)
        if form.is_valid(): # and request.recaptcha_is_valid:
            user = form.get_user()
            login(request, user)

            # Log user activity
            UserActivityLog.objects.create(
                user=user,
                action='login',
                ip_address=self.get_client_ip(request),
                user_agent=request.META.get('HTTP_USER_AGENT'),
                details={'login_type': 'regular'}
            )

            # Handle remember me
            if not form.cleaned_data.get('remember_me'):
                request.session.set_expiry(0)

            # Get the 'next' parameter if it exists
            next_url = request.GET.get('next')
            if next_url and self.is_safe_url(next_url):
                return redirect(next_url)

            # Redirect based on user type
            if user.is_superuser:
                return redirect('admin:index')
            elif user.user_type == 2:  # Dentist
                return redirect('Dentists:dashboard')
            else:
                return redirect('home')

        return render(request, self.template_name, {'form': form})

    def get_client_ip(self, request):
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        return (x_forwarded_for.split(',')[0] if x_forwarded_for
                else request.META.get('REMOTE_ADDR'))

    def is_safe_url(self, url):
        from django.utils.http import url_has_allowed_host_and_scheme
        return url_has_allowed_host_and_scheme(
            url=url,
            allowed_hosts={self.request.get_host()},
            require_https=self.request.is_secure(),
        )

class ProfileView(LoginRequiredMixin, BaseContextMixin, UpdateView):
    model = CustomUser
    form_class = UserProfileUpdateForm
    template_name = 'accounts/profile/profile.html'
    success_url = reverse_lazy('accounts:profile')

    def get_object(self, queryset=None):
        return self.request.user

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context.update({
            'user_settings': self.request.user.settings,
            'qualifications': self.request.user.qualifications.all(),
            'recent_activity': self.request.user.activity_logs.order_by(
                '-timestamp'
            )[:5],
        })
        return context

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, _('Profile updated successfully.'))

        # Log the profile update
        UserActivityLog.objects.create(
            user=self.request.user,
            action='profile_update',
            ip_address=self.request.META.get('REMOTE_ADDR'),
            details={'updated_fields': list(form.changed_data)}
        )

        return response

# @method_decorator(login_required, name='dispatch')
class UserSettingsView(LoginRequiredMixin, BaseContextMixin, UpdateView):
    model = UserSettings
    fields = [
        'email_notifications',
        'sms_notifications',
        'theme',
        'preferred_language',
        'items_per_page'
    ]
    template_name = 'accounts/profile/settings.html'
    success_url = reverse_lazy('accounts:profile')

    def get_object(self, queryset=None):
        settings, created = UserSettings.objects.get_or_create(user=self.request.user)
        return settings

    def form_valid(self, form):
        messages.success(self.request, _('Settings updated successfully.'))
        return super().form_valid(form)


from django.views.generic import UpdateView
from django.contrib import messages
from django.urls import reverse_lazy
from .models import UserSettings

class UserSettingsUpdateView(LoginRequiredMixin, UpdateView):
    model = UserSettings
    fields = [
        'email_notifications',
        'sms_notifications',
        'theme',
        'preferred_language',
        'items_per_page'
    ]
    template_name = 'accounts/profile/settings_update.html'
    success_url = reverse_lazy('accounts:profile')

    def get_object(self, queryset=None):
        # Get or create settings for the current user
        settings, created = UserSettings.objects.get_or_create(user=self.request.user)
        return settings

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, _('Settings updated successfully.'))
        return response

from django.views.generic import View
from django.shortcuts import render, redirect
from django.contrib import messages
from django.utils.translation import gettext_lazy as _

class TwoFactorSetupView(LoginRequiredMixin, View):
    template_name = 'accounts/auth/setup_2fa.html'

    def get(self, request):
        context = {
            'user': request.user,
            # Add any additional context you need
        }
        return render(request, self.template_name, context)

    def post(self, request):
        # Handle 2FA setup logic here
        try:
            # Your 2FA setup implementation
            messages.success(request, _('Two-factor authentication has been set up successfully.'))
            return redirect('accounts:profile')
        except Exception as e:
            messages.error(request, str(e))
            return render(request, self.template_name)

class QualificationManagementMixin:
    """Mixin for qualification-related views"""
    def get_queryset(self):
        return UserQualification.objects.filter(user=self.request.user)

class QualificationListView(LoginRequiredMixin, QualificationManagementMixin,
                          BaseContextMixin, ListView):
    model = UserQualification
    template_name = 'accounts/profile/qualifications.html'
    context_object_name = 'qualifications'
    paginate_by = 10

class QualificationCreateView(LoginRequiredMixin, QualificationManagementMixin,
                            BaseContextMixin, CreateView):
    model = UserQualification
    template_name = 'accounts/profile/qualification_form.html'
    fields = ['title', 'institution', 'date_obtained', 'document']
    success_url = reverse_lazy('accounts:qualifications')

    def form_valid(self, form):
        form.instance.user = self.request.user
        response = super().form_valid(form)
        messages.success(self.request, _('Qualification added successfully.'))
        return response

class QualificationDeleteView(LoginRequiredMixin, QualificationManagementMixin,
                            BaseContextMixin, DeleteView):
    model = UserQualification
    template_name = 'accounts/profile/qualification_confirm_delete.html'
    success_url = reverse_lazy('accounts:qualifications')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(request, _('Qualification deleted successfully.'))
        return response

from django.views.generic import ListView
from django.utils.translation import gettext_lazy as _
from django.contrib.auth.mixins import LoginRequiredMixin
from django.db.models import Q
from .models import UserActivityLog

class ActivityLogView(LoginRequiredMixin, ListView):
    model = UserActivityLog
    template_name = 'accounts/profile/activity_log.html'
    context_object_name = 'logs'
    paginate_by = 20
    ordering = ['-timestamp']

    def get_queryset(self):
        queryset = UserActivityLog.objects.filter(user=self.request.user)

        # Search
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(action__icontains=search) |
                Q(ip_address__icontains=search) |
                Q(user_agent__icontains=search)
            )

        # Date range
        start_date = self.request.GET.get('start_date')
        if start_date:
            queryset = queryset.filter(timestamp__date__gte=start_date)

        end_date = self.request.GET.get('end_date')
        if end_date:
            queryset = queryset.filter(timestamp__date__lte=end_date)

        # Action type filter
        actions = self.request.GET.getlist('action')
        if actions:
            queryset = queryset.filter(action__in=actions)

        # Status filter
        success = self.request.GET.getlist('success')
        if success:
            queryset = queryset.filter(success__in=[s == 'true' for s in success])

        # Ordering
        order_by = self.request.GET.get('order_by', '-timestamp')
        return queryset.order_by(order_by)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        queryset = self.get_queryset()

        context.update({
            'login_count': queryset.filter(action='login').count(),
            'security_count': queryset.filter(action='security').count(),
            'failed_count': queryset.filter(success=False).count(),
            'action_types': UserActivityLog.ACTION_TYPES,
            'selected_actions': self.request.GET.getlist('action'),
            'selected_status': self.request.GET.getlist('success'),
            'order_by': self.request.GET.get('order_by', '-timestamp'),
        })

        return context


import csv
from django.http import HttpResponse
from django.views import View
from reportlab.lib import colors
from reportlab.lib.pagesizes import letter
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle
from io import BytesIO

class ExportActivityLogView(LoginRequiredMixin, View):
    def get(self, request, *args, **kwargs):
        # Get the format from query parameters (default to CSV)
        export_format = request.GET.get('format', 'csv')

        # Get activity logs
        logs = UserActivityLog.objects.filter(user=request.user).order_by('-timestamp')

        # Apply filters if any
        if request.GET.get('start_date'):
            logs = logs.filter(timestamp__date__gte=request.GET.get('start_date'))
        if request.GET.get('end_date'):
            logs = logs.filter(timestamp__date__lte=request.GET.get('end_date'))
        if request.GET.getlist('action'):
            logs = logs.filter(action__in=request.GET.getlist('action'))
        if request.GET.getlist('success'):
            logs = logs.filter(success__in=[s == 'true' for s in request.GET.getlist('success')])

        if export_format == 'csv':
            return self._export_csv(logs)
        elif export_format == 'pdf':
            return self._export_pdf(logs)
        else:
            return HttpResponse('Unsupported format', status=400)

    def _export_csv(self, logs):
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename="activity_log.csv"'

        writer = csv.writer(response)
        # Write header
        writer.writerow([
            'Timestamp',
            'Action',
            'IP Address',
            'Browser',
            'Status',
            'Details'
        ])

        # Write data
        for log in logs:
            writer.writerow([
                log.timestamp.strftime('%Y-%m-%d %H:%M:%S'),
                log.get_action_display(),
                log.ip_address or 'N/A',
                log.user_agent or 'N/A',
                'Success' if log.success else 'Failed',
                str(log.details) if log.details else 'N/A'
            ])

        return response

    def _export_pdf(self, logs):
        response = HttpResponse(content_type='application/pdf')
        response['Content-Disposition'] = 'attachment; filename="activity_log.pdf"'

        buffer = BytesIO()
        doc = SimpleDocTemplate(buffer, pagesize=letter)
        elements = []

        # Define data for table
        data = [['Timestamp', 'Action', 'IP Address', 'Status']]  # Header row
        for log in logs:
            data.append([
                log.timestamp.strftime('%Y-%m-%d %H:%M:%S'),
                log.get_action_display(),
                log.ip_address or 'N/A',
                'Success' if log.success else 'Failed'
            ])

        # Create table
        table = Table(data)
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 14),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('TEXTCOLOR', (0, 1), (-1, -1), colors.black),
            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 1), (-1, -1), 12),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ]))

        elements.append(table)
        doc.build(elements)

        # Get PDF from buffer
        pdf = buffer.getvalue()
        buffer.close()
        response.write(pdf)

        return response




class PasswordManagementView(LoginRequiredMixin, BaseContextMixin, View):
    template_name = 'accounts/profile/change_password.html'
    form_class = ChangePasswordForm

    def get(self, request):
        form = self.form_class(request.user)
        return render(request, self.template_name, {'form': form})

    def post(self, request):
        form = self.form_class(request.user, request.POST)
        if form.is_valid():
            user = form.save()

            # Log password change
            UserActivityLog.objects.create(
                user=user,
                action='password_change',
                ip_address=request.META.get('REMOTE_ADDR'),
                details={'method': 'manual_change'}
            )

            # Update session to prevent session fixation
            update_session_auth_hash(request, user)

            messages.success(request, _('Your password was successfully updated.'))
            return redirect('accounts:profile')

        return render(request, self.template_name, {'form': form})

class PasswordResetView(AuthenticationViewMixin, BaseContextMixin, View):
    template_name = 'accounts/auth/password_reset.html'
    form_class = ResetPasswordForm
    email_template_name = 'accounts/email/password_reset_email.html'
    email_subject_template_name = 'accounts/email/password_reset_subject.txt'

    def get(self, request):
        return render(request, self.template_name, {'form': self.form_class()})

    def post(self, request):
        form = self.form_class(request.POST)
        if form.is_valid():
            email = form.cleaned_data['email']
            try:
                user = CustomUser.objects.get(email=email)
                self.send_password_reset_email(user)
                messages.success(
                    request,
                    _('Password reset instructions have been sent to your email.')
                )
                return redirect('accounts:login')
            except CustomUser.DoesNotExist:
                # Still show success message to prevent email enumeration
                messages.success(
                    request,
                    _('If an account exists with this email, '
                      'you will receive password reset instructions.')
                )
                return redirect('accounts:login')

        return render(request, self.template_name, {'form': form})

    def send_password_reset_email(self, user):
        token = default_token_generator.make_token(user)
        uid = urlsafe_base64_encode(force_bytes(user.pk))
        reset_url = reverse('accounts:password_reset_confirm',
                          kwargs={'uidb64': uid, 'token': token})

        context = {
            'user': user,
            'reset_url': f"{settings.DOMAIN}{reset_url}",
            'site_name': settings.SITE_NAME,
            'expiry_hours': 24,
        }

        subject = render_to_string(self.email_subject_template_name, context)
        subject = ''.join(subject.splitlines())  # Remove newlines
        html_message = render_to_string(self.email_template_name, context)
        plain_message = strip_tags(html_message)

        send_mail(
            subject=subject,
            message=plain_message,
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[user.email],
            html_message=html_message,
            fail_silently=False,
        )

class EmailVerificationView(BaseContextMixin, View):
    """Handle email verification links"""

    def get(self, request, uidb64, token):
        try:
            uid = force_str(urlsafe_base64_decode(uidb64))
            user = CustomUser.objects.get(pk=uid)

            if not account_activation_token.check_token(user, token):
                raise ValidationError(_('Verification link is invalid or expired'))

            if user.is_active:
                messages.info(request, _('Your email is already verified.'))
                return redirect('accounts:login')

            user.is_active = True
            user.email_verified = True
            user.email_verified_at = timezone.now()
            user.save(update_fields=['is_active', 'email_verified',
                                   'email_verified_at'])

            UserActivityLog.objects.create(
                user=user,
                action='email_verification',
                ip_address=request.META.get('REMOTE_ADDR'),
                details={'method': 'email_link'}
            )

            messages.success(
                request,
                _('Email verified successfully. You can now log in.')
            )
            return redirect('accounts:login')

        except (TypeError, ValueError, OverflowError, CustomUser.DoesNotExist,
                ValidationError) as e:
            messages.error(
                request,
                _('Verification link is invalid or expired. '
                  'Please request a new one.')
            )
            return redirect('accounts:resend_verification')

class ResendVerificationView(AuthenticationViewMixin, BaseContextMixin, View):
    template_name = 'accounts/auth/resend_verification.html'
    form_class = ResendVerificationForm

    def get(self, request):
        return render(request, self.template_name, {'form': self.form_class()})

    def post(self, request):
        form = self.form_class(request.POST)
        if form.is_valid():
            email = form.cleaned_data['email']
            try:
                user = CustomUser.objects.get(email=email, is_active=False)
                self.send_verification_email(user)
                messages.success(
                    request,
                    _('Verification email has been resent. '
                      'Please check your inbox.')
                )
                return redirect('accounts:login')
            except CustomUser.DoesNotExist:
                messages.error(
                    request,
                    _('No unverified account found with this email address.')
                )
        return render(request, self.template_name, {'form': form})

# @method_decorator(login_required, name='dispatch')
class ProfileImageUpdateView(View):
    def post(self, request):
        try:
            if 'profile_image' not in request.FILES:
                raise ValidationError(_('No image provided'))

            image = request.FILES['profile_image']

            # Validate image
            validate_image_file(image)

            # Process image
            processed_image = process_profile_image(image)

            # Save image
            request.user.profile_image = processed_image
            request.user.save(update_fields=['profile_image'])

            # Log activity
            UserActivityLog.objects.create(
                user=request.user,
                action='profile_image_update',
                ip_address=request.META.get('REMOTE_ADDR')
            )

            return JsonResponse({
                'success': True,
                'message': _('Profile image updated successfully.'),
                'image_url': request.user.profile_image.url
            })

        except ValidationError as e:
            return JsonResponse({
                'success': False,
                'message': str(e)
            }, status=400)
        except Exception as e:
            logger.error(f"Profile image update error: {str(e)}")
            return JsonResponse({
                'success': False,
                'message': _('An error occurred while updating profile image.')
            }, status=500)

@login_required
def logout_view(request):
    """Handle user logout with activity logging"""
    if request.user.is_authenticated:
        UserActivityLog.objects.create(
            user=request.user,
            action='logout',
            ip_address=request.META.get('REMOTE_ADDR'),
            details={'logout_type': 'manual'}
        )
        logout(request)
        messages.success(request, _('You have been successfully logged out.'))
    return redirect('accounts:login')

# Admin views
class UserManagementView(SuperUserRequiredMixin, BaseContextMixin, ListView):
    model = CustomUser
    template_name = 'accounts/admin/user_management.html'
    context_object_name = 'users'
    paginate_by = 25
    ordering = ['-date_joined']

    def get_queryset(self):
        queryset = super().get_queryset()
        search_query = self.request.GET.get('q')
        if search_query:
            queryset = queryset.filter(
                Q(email__icontains=search_query) |
                Q(first_name__icontains=search_query) |
                Q(last_name__icontains=search_query)
            )
        return queryset.select_related('settings')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context.update({
            'total_users': CustomUser.objects.count(),
            'active_users': CustomUser.objects.filter(is_active=True).count(),
            'staff_users': CustomUser.objects.filter(is_staff=True).count(),
            'search_query': self.request.GET.get('q', ''),
        })
        return context

# Error handling views
def custom_404(request, exception):
    return render(request, 'accounts/errors/404.html', status=404)

def custom_500(request):
    return render(request, 'accounts/errors/500.html', status=500)

def custom_403(request, exception):
    return render(request, 'accounts/errors/403.html', status=403)



# accounts/views.py

class UserDetailView(SuperUserRequiredMixin, BaseContextMixin, DetailView):
    model = CustomUser
    template_name = 'accounts/admin/user_detail.html'
    context_object_name = 'user_detail'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        user = self.get_object()
        context.update({
            'activity_logs': user.activity_logs.order_by('-timestamp')[:10],
            'qualifications': user.qualifications.all(),
            'total_logins': user.activity_logs.filter(action='login').count(),
            'last_login': user.activity_logs.filter(
                action='login'
            ).order_by('-timestamp').first(),
            'account_age': (timezone.now() - user.date_joined).days,
            'departments': user.departments.all(),
            'is_active': user.is_active,
            'is_staff': user.is_staff,
            'is_superuser': user.is_superuser,
            'date_joined': user.date_joined,
            # 'email_verified': user.email_verified,
        })
        return context

# accounts/views.py

from .forms import UserAdminForm  # Add this import at the top

class UserUpdateView(SuperUserRequiredMixin, BaseContextMixin, UpdateView):
    model = CustomUser
    form_class = UserAdminForm
    template_name = 'accounts/admin/user_edit.html'
    success_url = reverse_lazy('accounts:user_management')

    def form_valid(self, form):
        response = super().form_valid(form)
        user = self.object

        # Log the changes
        changed_fields = form.changed_data
        UserActivityLog.objects.create(
            user=self.request.user,  # Admin who made the changes
            action='user_update',
            ip_address=self.request.META.get('REMOTE_ADDR'),
            details={
                'modified_user_id': user.id,
                'modified_fields': changed_fields
            }
        )

        if 'password_change' in changed_fields:
            # Log password change separately
            UserActivityLog.objects.create(
                user=user,
                action='password_change',
                ip_address=self.request.META.get('REMOTE_ADDR'),
                details={'changed_by_admin': self.request.user.id}
            )

        messages.success(
            self.request,
            _('User "{}" has been updated successfully.').format(user.get_full_name())
        )
        return response

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['user_full_name'] = self.object.get_full_name()
        return context




class UserDeleteView(SuperUserRequiredMixin, BaseContextMixin, DeleteView):
    model = CustomUser
    template_name = 'accounts/admin/user_delete.html'
    success_url = reverse_lazy('accounts:user_management')
    context_object_name = 'user_to_delete'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        user = self.get_object()
        context.update({
            'user_full_name': user.get_full_name(),
            'user_email': user.email,
            'account_age': (timezone.now() - user.date_joined).days,
            'is_staff': user.is_staff,
            'is_superuser': user.is_superuser,
            # Add any related objects that will be affected
            'related_cases': getattr(user, 'cases', []).count(),
            'related_qualifications': user.qualifications.count(),
            'activity_logs': user.activity_logs.count(),
        })
        return context

    def send_password_reset_email(self, user):
        token = default_token_generator.make_token(user)
        uid = urlsafe_base64_encode(force_bytes(user.pk))
        reset_url = reverse('accounts:password_reset_confirm',
                          kwargs={'uidb64': uid, 'token': token})

        context = {
            'user': user,
            'reset_url': f"{settings.DOMAIN}{reset_url}",
            'site_name': settings.SITE_NAME,
            'expiry_hours': 24,
        }

        subject = render_to_string(self.email_subject_template_name, context)
        subject = ''.join(subject.splitlines())  # Remove newlines
        html_message = render_to_string(self.email_template_name, context)
        plain_message = strip_tags(html_message)

        try:
            send_mail(
                subject=subject,
                message=plain_message,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[user.email],
                html_message=html_message,
                fail_silently=False,
            )
        except Exception as e:
            messages.error(
                request,
                _('Password reset email could not be sent. Please try again later.')
            )
            return redirect('accounts:password_reset')

# accounts/views.py


# accounts/views.py

from django.views.generic.edit import UpdateView
from django.contrib.auth.mixins import LoginRequiredMixin, UserPassesTestMixin
from django.contrib import messages
from django.urls import reverse_lazy
from .forms import SystemSettingsForm
from .models import SystemSettings

class SystemSettingsView(LoginRequiredMixin, UserPassesTestMixin, UpdateView):
    model = SystemSettings
    form_class = SystemSettingsForm
    template_name = 'accounts/settings/system_settings.html'
    success_url = reverse_lazy('accounts:system_settings')

    def test_func(self):
        # Only allow access to superusers (administrators)
        return self.request.user.is_superuser

    def get_object(self, queryset=None):
        # Retrieve the singleton instance of SystemSettings
        obj, created = SystemSettings.objects.get_or_create(pk=1)
        return obj

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, _("System settings have been updated successfully."))
        
        # Log the settings update
        UserActivityLog.objects.create(
            user=self.request.user,
            action='system_settings_update',
            ip_address=self.request.META.get('REMOTE_ADDR'),
            details={'modified_fields': form.changed_data}
        )
        
        return response

    def form_invalid(self, form):
        messages.error(self.request, _("There was an error updating the system settings."))
        return super().form_invalid(form)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['section'] = 'system_settings'
        return context




from django.shortcuts import render
from django.db.models import Count, CharField
from django.db.models.functions import TruncDay, Cast
from django.core.serializers.json import DjangoJSONEncoder
from django.utils import timezone
from dateutil.relativedelta import relativedelta
import json
from billing.models import Invoice

from django.db.models import When, F, Sum, DecimalField, Value, ExpressionWrapper, CharField, Count
from case.models import Case, Department, Task, CaseStatusHistory
from Dentists.models import Dentist
from datetime import timedelta
from django.db.models import Sum

from django.db.models import F, Sum, When, Value
from django.db.models.fields import DecimalField
from django.db.models.functions import Coalesce
from billing.models import Invoice
from django.db.models.functions import TruncDate, TruncMonth

from django.contrib.auth.decorators import permission_required
#############################################################################################
# @login_required(login_url='accounts/login')
# @permission_required('account.can_view_dashboard', raise_exception=True)

from django.shortcuts import render
from django.utils import timezone
from datetime import timedelta
from django.db.models import Count, Sum, Q
from django.db.models.functions import TruncDate, TruncMonth
from django.core.serializers.json import DjangoJSONEncoder
from dateutil.relativedelta import relativedelta
import json

from case.models import Case, Department
from Dentists.models import Dentist
from billing.models import Invoice

# accounts/views.py (or your relevant views file)

from django.shortcuts import render
from django.contrib.auth.decorators import login_required, permission_required
from django.utils import timezone
from datetime import timedelta, datetime
# import random # Not used
from dateutil.relativedelta import relativedelta
from django.db.models import (
    Count, Q, Avg, F, ExpressionWrapper, DurationField, Case as DBCase, When, DecimalField, Min
)
from django.db.models.functions import TruncDate, TruncMonth, Cast, Coalesce
from django.core.serializers.json import DjangoJSONEncoder
import json
# from decimal import Decimal # Not used directly in this function snippet

# Import models from relevant apps
from case.models import Case, Department # CaseItem might be needed for costs, but not for this part
from Dentists.models import Dentist
# from items.models import ItemRawMaterial # Import if calculating costs

# --- Helper Functions (Keep these as they are useful) ---
def calculate_average_duration(queryset, start_field='received_date_time', end_field='ship_date_time'):
    """Calculates average duration between two datetime fields in days."""
    valid_cases = queryset.filter(
        **{f"{start_field}__isnull": False},
        **{f"{end_field}__isnull": False}
    )
    duration_expression = ExpressionWrapper(F(end_field) - F(start_field), output_field=DurationField())
    average_duration_result = valid_cases.aggregate(avg_duration=Avg(duration_expression))['avg_duration']

    if average_duration_result:
        # Ensure duration is positive before calculating days
        if average_duration_result.total_seconds() >= 0:
             return average_duration_result.total_seconds() / (60 * 60 * 24) # Convert to days
        else:
             # Handle potential negative durations if end_field < start_field
             print(f"Warning: Negative duration calculated ({average_duration_result}). Returning 0.")
             return 0
    return 0

def calculate_on_time_rate(queryset, deadline_field='deadline', end_field='ship_date_time'):
    """Calculates the percentage of cases completed on or before the deadline."""
    relevant_cases = queryset.filter(
        **{f"{deadline_field}__isnull": False},
        **{f"{end_field}__isnull": False}
    )
    total_relevant = relevant_cases.count()

    if total_relevant > 0:
        completed_on_time = relevant_cases.filter(
            **{f"{end_field}__lte": F(deadline_field)}
        ).count()
        return (completed_on_time / total_relevant) * 100
    return 0


# --- Main View Function (Focus on Trend Data Generation) ---
@login_required(login_url='accounts:login')
def home(request):
    """
    Displays the main dashboard with enhanced metrics and trends.
    Focus on ensuring trend data generation is correct.
    """
    try:
        # Initialize response data
        context = {
            'error': None,
            'total_cases_count': 0,
            'cases_today_count': 0,
            'cases_this_week_count': 0,
            'cases_this_month_count': 0,
            'overdue_cases_count': 0,
            'ready_to_ship_count': 0,
            'avg_completion_days': 0,
            'on_time_completion_rate': 0,
            'week_difference': 0,
            'month_difference': 0,
            'status_counts': [],
            'priority_counts': [],
            'department_counts': [],
            'latest_cases': [],
            'top_dentists': [],
            'selected_range': '30'
        }
        
        print("\n--- Executing home view ---")
        # --- Date Calculations ---
        print("\n--- DEBUG: Date Calculations ---")
        # Check if there are any cases with received_date_time
        has_cases_with_received_date = Case.objects.filter(received_date_time__isnull=False).exists()
        print(f"DEBUG: Any cases with received_date_time? {has_cases_with_received_date}")

        # Get the count of cases with received_date_time
        cases_with_received_date_count = Case.objects.filter(received_date_time__isnull=False).count()
        print(f"DEBUG: Number of cases with received_date_time: {cases_with_received_date_count}")

        # Get the latest case date
        latest_case_date = Case.objects.filter(received_date_time__isnull=False).order_by('-received_date_time__date').values_list('received_date_time__date', flat=True).first()
        print(f"DEBUG: Latest case date from DB: {latest_case_date}")

        today = latest_case_date or timezone.now().date()
        print(f"DEBUG: Using today as: {today}")

        date_range_str = request.GET.get('range', '30')
        valid_ranges = [7, 30, 90, 180, 365, 730] # Align with HTML select options
        try:
            days_in_range = int(date_range_str)
            if days_in_range not in valid_ranges:
                days_in_range = 30 # Default fallback
        except (ValueError, TypeError):
            days_in_range = 30

        start_date_selected_range = today - timedelta(days=days_in_range - 1)
        print(f"DEBUG: Selected Range: {days_in_range} days (from {start_date_selected_range} to {today})")

        start_30_days_ago = today - timedelta(days=29)
        start_12_months_ago = (today - relativedelta(months=12) + timedelta(days=1)).replace(day=1)
        start_24_months_ago = (today - relativedelta(months=24) + timedelta(days=1)).replace(day=1)

        earliest_case_date_obj = Case.objects.filter(received_date_time__isnull=False).aggregate(min_date=Min('received_date_time'))['min_date']
        print(f"DEBUG: Earliest case date from DB: {earliest_case_date_obj}")

        if earliest_case_date_obj:
             # Ensure start_all_time is a date object, not datetime
            start_all_time = earliest_case_date_obj.date().replace(day=1)
        else:
            start_all_time = today.replace(day=1)
        print(f"DEBUG: Using start_all_time as: {start_all_time}")

        start_this_week = today - timedelta(days=today.weekday())
        start_last_week = start_this_week - timedelta(weeks=1)
        start_this_month = today.replace(day=1)
        start_last_month = start_this_month - relativedelta(months=1)

        print(f"DEBUG: 30 Day Trend Range: {start_30_days_ago} to {today}")
        print(f"DEBUG: 12 Month Trend Range: {start_12_months_ago} to {today}")
        print(f"DEBUG: 24 Month Trend Range: {start_24_months_ago} to {today}")
        print(f"DEBUG: All Time Trend Range: {start_all_time} to {today}")

        # --- Base Querysets ---
        print("\n--- DEBUG: Base Querysets ---")

        # Check if there are any cases at all
        total_cases = Case.objects.count()
        print(f"DEBUG: Total cases in database: {total_cases}")

        # Check if there are any cases with received_date_time in the selected range
        cases_in_selected_range_qs = Case.objects.select_related(
            'patient', 'dentist', 'responsible_department'
        ).filter(
            received_date_time__isnull=False,
            received_date_time__date__gte=start_date_selected_range,
            received_date_time__date__lte=today
        )
        print(f"DEBUG: Cases in selected range ({days_in_range} days): {cases_in_selected_range_qs.count()}")

        # Get a sample of cases to check their received_date_time values
        sample_cases = Case.objects.filter(received_date_time__isnull=False).order_by('-received_date_time')[:5]
        print("DEBUG: Sample cases with received_date_time:")
        for case in sample_cases:
            print(f"DEBUG:   Case #{case.case_number}: received_date_time={case.received_date_time}, status={case.status}")

        # Check for 30-day trend
        cases_for_30_day_trend_qs = Case.objects.filter(
             received_date_time__isnull=False,
             received_date_time__date__gte=start_30_days_ago,
             received_date_time__date__lte=today
        )
        print(f"DEBUG: Cases for 30-day trend: {cases_for_30_day_trend_qs.count()}")
        if cases_for_30_day_trend_qs.count() > 0:
            print(f"DEBUG: First case in 30-day trend: {cases_for_30_day_trend_qs.first().case_number}, received_date_time={cases_for_30_day_trend_qs.first().received_date_time}")

        # Check for 12-month trend
        cases_for_12_month_trend_qs = Case.objects.filter(
            received_date_time__isnull=False,
            received_date_time__date__gte=start_12_months_ago,
            received_date_time__date__lte=today
        )
        print(f"DEBUG: Cases for 12-month trend: {cases_for_12_month_trend_qs.count()}")
        if cases_for_12_month_trend_qs.count() > 0:
            print(f"DEBUG: First case in 12-month trend: {cases_for_12_month_trend_qs.first().case_number}, received_date_time={cases_for_12_month_trend_qs.first().received_date_time}")

        # Check for 24-month trend
        cases_for_24_month_trend_qs = Case.objects.filter(
            received_date_time__isnull=False,
            received_date_time__date__gte=start_24_months_ago,
            received_date_time__date__lte=today
        )
        print(f"DEBUG: Cases for 24-month trend: {cases_for_24_month_trend_qs.count()}")
        if cases_for_24_month_trend_qs.count() > 0:
            print(f"DEBUG: First case in 24-month trend: {cases_for_24_month_trend_qs.first().case_number}, received_date_time={cases_for_24_month_trend_qs.first().received_date_time}")

        # Check for all-time trend
        cases_for_all_time_trend_qs = Case.objects.filter(
            received_date_time__isnull=False,
            received_date_time__date__gte=start_all_time,
            received_date_time__date__lte=today
        )
        print(f"DEBUG: Cases for all-time trend: {cases_for_all_time_trend_qs.count()}")
        if cases_for_all_time_trend_qs.count() > 0:
            print(f"DEBUG: First case in all-time trend: {cases_for_all_time_trend_qs.first().case_number}, received_date_time={cases_for_all_time_trend_qs.first().received_date_time}")


        # --- KPIs & Other Chart Data ---
        total_cases_count = Case.objects.count()
        cases_today_count = cases_in_selected_range_qs.filter(received_date_time__date=today).count()
        cases_this_week_count = cases_in_selected_range_qs.filter(received_date_time__date__gte=start_this_week).count()
        cases_last_week_count_comp = Case.objects.filter(
            received_date_time__isnull=False,
            received_date_time__date__gte=start_last_week,
            received_date_time__date__lt=start_this_week
        ).count()
        cases_this_month_count = cases_in_selected_range_qs.filter(received_date_time__date__gte=start_this_month).count()
        cases_last_month_count_comp = Case.objects.filter(
             received_date_time__isnull=False,
             received_date_time__date__gte=start_last_month,
             received_date_time__date__lt=start_this_month
        ).count()

        week_difference = cases_this_week_count - cases_last_week_count_comp
        month_difference = cases_this_month_count - cases_last_month_count_comp

        overdue_statuses = ['pending_acceptance', 'on_hold', 'in_progress', 'ready_to_ship']
        overdue_cases_count = cases_in_selected_range_qs.filter(
            deadline__lt=today,
            status__in=overdue_statuses
        ).count()
        ready_to_ship_count = cases_in_selected_range_qs.filter(status='ready_to_ship').count()

        completed_statuses = ['closed', 'delivered', 'shipped']
        completed_in_range = cases_in_selected_range_qs.filter(status__in=completed_statuses)
        avg_completion_days = calculate_average_duration(completed_in_range)
        on_time_completion_rate = calculate_on_time_rate(completed_in_range)

        status_counts = cases_in_selected_range_qs.values('status').annotate(total=Count('pk')).order_by('-total')
        priority_map = dict(Case.PRIORITY_CHOICES)
        priority_counts_raw = cases_in_selected_range_qs.values('priority').annotate(total=Count('pk')).order_by('priority')
        priority_counts = [
            {'priority': priority_map.get(item['priority'], f"Unknown ({item['priority']})"), 'total': item['total']}
            for item in priority_counts_raw
        ]
        department_counts = cases_in_selected_range_qs.exclude(responsible_department__isnull=True).values(
            'responsible_department__name'
        ).annotate(total_cases=Count('pk')).order_by('-total_cases')



        # --- Table Data ---
        latest_cases = cases_in_selected_range_qs.order_by('-received_date_time')[:5]
        top_dentists = Dentist.objects.annotate(
            total_cases_in_range=Count(
                'dentist_cases',
                filter=Q(dentist_cases__pk__in=cases_in_selected_range_qs.values('pk'))
            )
        ).filter(total_cases_in_range__gt=0).order_by('-total_cases_in_range')[:5]

        context = {
            'total_cases_count': total_cases_count,
            'cases_today_count': cases_today_count,
            'cases_this_week_count': cases_this_week_count,
            'cases_this_month_count': cases_this_month_count,
            'overdue_cases_count': overdue_cases_count,
            'ready_to_ship_count': ready_to_ship_count,
            'avg_completion_days': round(avg_completion_days, 1),
            'on_time_completion_rate': round(on_time_completion_rate, 1),
            'week_difference': week_difference,
            'month_difference': month_difference,
            'status_counts': status_counts,
            'priority_counts': priority_counts,
            'department_counts': department_counts,
            'latest_cases': latest_cases,
            'top_dentists': top_dentists,
            'selected_range': str(days_in_range)
        }

        return render(request, 'dashboard/home.html', context)

    except Exception as e:
        # Log the error for debugging
        import traceback
        traceback.print_exc()
        
        # Return a minimal context with error message
        error_context = {
            'error': str(e),
            'total_cases_count': 0,
            'cases_today_count': 0,
            'cases_this_week_count': 0,
            'cases_this_month_count': 0,
            'overdue_cases_count': 0,
            'ready_to_ship_count': 0,
            'avg_completion_days': 0,
            'on_time_completion_rate': 0,
            'week_difference': 0,
            'month_difference': 0,
            'status_counts': [],
            'priority_counts': [],
            'department_counts': [],
            'latest_cases': [],
            'top_dentists': [],
            'selected_range': '30'
        }
        return render(request, 'dashboard/home.html', error_context)



@login_required(login_url='accounts:login')
def home_vue(request):
    """
    Vue.js version of the home dashboard
    """
    try:
        # --- Date Calculations ---
        from django.utils import timezone
        from django.db.models import Count
        from django.db.models.functions import TruncDate, TruncMonth
        import json
        from datetime import datetime, timedelta
        from dateutil.relativedelta import relativedelta
        from case.models import Case, Department
        from Dentists.models import Dentist

        # Get date range from request or default to 30 days
        days_in_range = int(request.GET.get('range', 30))
        today = timezone.now().date()

        # Calculate date ranges
        start_date = today - timedelta(days=days_in_range)
        start_30_days_ago = today - timedelta(days=30)
        start_12_months_ago = today - relativedelta(months=12)

        # Calculate week and month ranges
        start_this_week = today - timedelta(days=today.weekday())
        start_last_week = start_this_week - timedelta(days=7)
        start_this_month = today.replace(day=1)
        start_last_month = (start_this_month - timedelta(days=1)).replace(day=1)

        # Get case counts
        total_cases_count = Case.objects.count()
        cases_today_count = Case.objects.filter(received_date_time__date=today).count()
        cases_this_week_count = Case.objects.filter(
            received_date_time__date__gte=start_this_week,
            received_date_time__date__lte=today
        ).count()
        cases_this_month_count = Case.objects.filter(
            received_date_time__date__gte=start_this_month,
            received_date_time__date__lte=today
        ).count()

        # Get comparison counts
        cases_last_week_count_comp = Case.objects.filter(
            received_date_time__date__gte=start_last_week,
            received_date_time__date__lt=start_this_week
        ).count()
        cases_last_month_count_comp = Case.objects.filter(
            received_date_time__date__gte=start_last_month,
            received_date_time__date__lt=start_this_month
        ).count()

        week_difference = cases_this_week_count - cases_last_week_count_comp
        month_difference = cases_this_month_count - cases_last_month_count_comp

        # Get cases in selected range
        cases_in_selected_range_qs = Case.objects.filter(
            received_date_time__date__gte=start_date,
            received_date_time__date__lte=today
        )

        # Get overdue and ready to ship counts
        overdue_cases_count = cases_in_selected_range_qs.filter(
            deadline__lt=today,
            status__in=['pending_acceptance', 'on_hold', 'in_progress', 'ready_to_ship']
        ).count()
        ready_to_ship_count = cases_in_selected_range_qs.filter(status='ready_to_ship').count()

        # Get latest cases
        latest_cases = cases_in_selected_range_qs.order_by('-received_date_time')[:10]

        # Get top dentists
        top_dentists = Dentist.objects.annotate(
            total_cases_last_year=Count(
                'dentist_cases',
                filter=Case.objects.filter(
                    created_at__date__gte=today - timedelta(days=365)
                ).values('case_number').query
            )
        ).order_by('-total_cases_last_year')[:5]

        # Calculate metrics for completed cases
        completed_cases = cases_in_selected_range_qs.filter(
            status__in=['closed', 'delivered', 'shipped']
        )

        avg_completion_days = 0
        on_time_completion_rate = 0

        total_completed = completed_cases.count()
        if total_completed > 0:
            # Calculate average completion days
            total_days = 0
            on_time_count = 0

            for case in completed_cases:
                if case.actual_completion:
                    days_taken = (case.actual_completion.date() - case.created_at.date()).days
                    total_days += days_taken

                    # Check if completed on time
                    if case.deadline and case.actual_completion.date() <= case.deadline:
                        on_time_count += 1

            avg_completion_days = total_days / total_completed if total_days > 0 else 0
            on_time_completion_rate = (on_time_count / total_completed) * 100 if total_completed > 0 else 0

        # Prepare context with all necessary data
        context = {
            'total_cases_count': total_cases_count,
            'cases_today_count': cases_today_count,
            'cases_this_week_count': cases_this_week_count,
            'cases_this_month_count': cases_this_month_count,
            'overdue_cases_count': overdue_cases_count,
            'ready_to_ship_count': ready_to_ship_count,
            'avg_completion_days': round(avg_completion_days, 1),
            'on_time_completion_rate': round(on_time_completion_rate, 1),
            'week_difference': week_difference,
            'month_difference': month_difference,
            'latest_cases': latest_cases,
            'top_dentists': top_dentists,
            'selected_range': str(days_in_range)
        }

        return render(request, 'home_vue.html', context)

    except Exception as e:
        # Log the error for debugging
        import traceback
        traceback.print_exc()
        
        # Return a minimal context with error message
        error_context = {
            'error': str(e),
            'total_cases_count': 0,
            'cases_today_count': 0,
            'cases_this_week_count': 0,
            'cases_this_month_count': 0,
            'overdue_cases_count': 0,
            'ready_to_ship_count': 0,
            'avg_completion_days': 0,
            'on_time_completion_rate': 0,
            'week_difference': 0,
            'month_difference': 0,
            'latest_cases': [],
            'top_dentists': [],
            'selected_range': '30'
        }
        return render(request, 'home_vue.html', error_context)
